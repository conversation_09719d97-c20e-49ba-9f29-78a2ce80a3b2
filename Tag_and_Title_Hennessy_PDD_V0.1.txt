




Tag and Title
Process Definition Document
Version 0.01













document version control
This document is subject to version control. Any modifications, amendments or otherwise are subject to review by all parties involved. Until a final release can be agreed upon, all versions are to be considered a draft (0.x).


open items
The following list of open items should be clarified and closed before sign-off.








Introduction
about this document
The Process Definition Document (PDD) captures the flow and steps of a business process to be automated.
This document describes the functional requirements of the automation solution for the Tag and Title process. The document covers the current state of the process, the future state of the process, the functional description for the solution, and the handling of exceptions and errors. Likewise, it outlines the reporting and dashboard requirements.


Process Documentation
1.1 Process Summary
This document describes the functional requirements of the automation solution for the Tag and Title process. The document covers the current state of the process, the future state of the process, the functional description for the solution, and the handling of exceptions and errors. Likewise, it outlines the reporting and dashboard requirements.
1.2 Objectives
The objectives of the solution are to:
Automate the manual and repetitive tasks involved in the GA Tag and Title process.
Reduce the risk of errors and increase the accuracy and consistency of the process output.
Improve the efficiency and productivity of the process.
Enhance the visibility and traceability of the process performance and status.

1.3 Technologies Involved in the Process


2. Prerequisites for Process Build

The implementation of the process follows the completion of the infrastructure setup. The following are expected to be in place at the start of the Build Phase:

DLR application must be accessible and with access granted.
Reynolds application must be accessible and ready to be used.


2.1 Key Contacts
The following table lists the key contacts for the solution development and implementation.

2.2 Terminology



AS IS Process definition

3. Overview
The Tag and Title process for Hennesy is designed to streamline documents management through AI-driven solutions. The process eliminates manual intervention, optimizes accuracy, and improves efficiency.

3.1 AS IS Process Map

The following steps show the high-level process maps of the AS IS process.
Below is a process map outlining the current (AS-IS) Tag and Title process, highlighting manual steps, delays, and inefficiencies.











3.2 AS IS Detailed Process Description



3.3 Known Business Exceptions


3.4 Summary of Pain Points


TO BE Process Definition
This section describes the future state of the Tag and Title process, including the detailed TO BE process map, the in scope and out of scope for the solution, the exceptions handling, the applications errors handling, and the reporting requirements.

4. TO BE Detailed Process Description
The following statements describe the hierarchy level of the entities involved in the process, a diagram to illustrate in the next image:


4.1 Process Entities

Reynolds & Reynolds (R&R)
Source of deal data
Stores scanned deal documents (Deal Jacket)
Generates assignment reports

Dealer DMV (Georgia)
Interface to submit deals to the Georgia Department of Revenue
Stores tax and title application data

Bill of Sale
The Bill of Sale is a legal document that records the sale transaction between the dealership and the buyer. It serves as proof of purchase and outlines the financial terms, buyer and vehicle information, and any trade-in details.
In the Tag & Title process, the Bill of Sale is critical for:
Verifying sale price and tax calculations (TAVT)
Confirming vehicle ownership
Matching buyer details with the MV1 and Driver’s License
Validating trade-in and lien information
Fields to be extracted and validated:




Driver License
The Driver’s License is used to:
Verify the identity of the buyer and co-buyer
Confirm the state-provided address (used to calculate county and tax jurisdiction)
Ensure accuracy of personal details for title registration
Driver’s License are scanned and uploaded into the Reynolds Deal Jacket and later used for matching with the MV1 and Dealer DMV data.
Fields to be extracted and validated:




MV-1

The MV-1 (Title Application) is the official Georgia state form required for registering and titling a vehicle. It includes buyer, vehicle, transaction, and lien information that must be accurate and consistent with other documents.
It is considered the anchor document in the title package and is used to:
Register the vehicle with the Georgia MVD
Apply for a new title (with or without lien)
Trigger tax calculation and plate issuance
Validate ownership, lien, and trade status
Fields to be extracted and validated:




MV-7D

The MV-7D is Georgia’s motor vehicle reassignment form, commonly called the Red Form due to its red color band. It is used when:
A dealership transfers ownership of a used vehicle
The title does not have space for additional reassignment
A formal paper trail of the seller-to-buyer chain is required
In the Tag & Title process, it is especially critical when:
The vehicle is used
There are multiple transfers before reaching the final buyer
Supporting documents must show consistent VIN, mileage, and buyer/seller names
Fields to be extracted and validated:





Title/MSO

The Title (for used vehicles) or MSO (for new vehicles) is the official ownership document that legally proves who owns the vehicle. It must accompany the registration and tax process and is a non-negotiable requirement for title submission to the Georgia DMV via Dealer DMV.
In the Tag & Title process, the Title or MSO is used to:
Confirm the original or prior ownership
Validate VIN and vehicle information
Verify lienholder and release of lien (if applicable)
Ensure the vehicle was properly reassigned to the current dealer
Support legal chain of ownership for reassignment
Fields to be extracted and validated:



Reassignment

The Reassignment Form, officially known as the MV-7D in Georgia, is used when:
The original title has no more space for dealer-to-dealer transfers
A used vehicle is sold to a dealership prior to the end buyer
There is a need to document the chain of ownership legally between multiple parties
In the Tag and Title process, this form serves as:
The legal bridge between the prior owner and the current dealer
A critical validation point for matching VIN, mileage, and buyer/seller info
Proof of accurate odometer disclosure, which is federally required
Fields to be extracted and validated (same as MV-7D):


      4.2 Assumptions and Restrictions
The process will run every day at time xyz
The process will extract document fields and will verify that the data is accurate by executing defined business rules.
After inserting items in ARIA, the process will enter the documents in Reynolds.
Records that cannot be processed automatically in Aria will be assigned the status Needs Attention. The user must review and process them manually in Aria, then the invoice will follow the workflow after reviewing the missing data.






      4.3 Process Steps








4.4 Known Business Exceptions Handling

4.5 In Scope for the Solution
Documents will be received and stored in ARIA for human review and traceability. Aria will receive the documents containing information about used or new cars deals. The details and conditions of this submission capture are described below. This information will be stored and displayed in ARIA. Users will have access to ARIA.

4.5.1 The list view
The list view is the main screen where the user can see all the received documents group by VIN that are in the system and perform searches on them using the existing filters in each field. It will also be possible to filter by status, using the summary buttons, which show the records found in each status. Additionally, on this screen the user will find an option to export the records that are visible as a CSV file.




The fields that will be shown in this view and their origin are detailed in the table below


With the Manage columns button, it is possible to make changes to the view to hide, show or sort the columns. Each user can have a customized view using this option.
Cases will automatically be assigned the first time a user see the content. Users can unassign the case by clicking the button or change the assigned person at the table view by selecting the case and clicking the Assign action.








4.5.2 The detail view
The detailed view will display a header with relevant information about the case and the data received, as can be seen in the image below.



Every case will be validated and categorized at its proper status. Within this view, staffs can track and manage every case. Cases will have a person assigned to it, that will be the staff managing the request. 


4.5.2 ARIA statuses and actions
///Diagram

Loading: case received
Needs attention: case need human intervention, validations failed.
Complete: data has been entered into Reynolds.







4.6 Out of Scope for the solution



System Step by Step Guide

5.1 Reynolds

Login at the application
Navigate to Applications menu
Select F&I/Desking

Or setup an Application Favourites where you can define the Store associated to the F/I Desking

Type the Deal number coming from the Report. 


Click Search (F5).
Deal information will be displayed.




Press Cntrl + J to display the list of documents.
The list of documents will show up.


Download all documents that has Signed in green.


5.2 DLR (1:20:03)

Navigate to DLR portal 
Enter username and password
Click in Login
Dashboard will be displayed


Use the top search to enter the Dealer Number (from report)

The list of deals will be displayed.

Check Deal Date to make sure it's the most recent version.
Click on the Deal row.








Verify the information based on the extracted information.
Start and vehicle information

Trade in


Lessor


Customers

Liens

Title Brands 
Registration
Tax Calc – Information comes from Bill of Sale.

Summary
EForms

Required Document Upload steps:
Under eForms tab, click on Choose Files

Upload the following document types: 
BOL
MV1
MV7D
Driver License
Title
Reassignment




Additional Requirements and Notes
6.1 Business Rules 
Field Mismatches Across Documents
VIN Mismatch: VIN differs between MV1, BOS, Title/MSO, Reassignment, or Dealer DMV.
Odometer Mismatch: Mileage on BOS, Reassignment (MV-7D), Title, and MV1 are inconsistent.
Buyer Name Mismatch: Buyer/co-buyer name does not match between MV1, DL, BOS, and DMV entry.
Title Chain Broken or Not Assigned: Missing reassignment between owner and dealership.
Buyer Address or County Mismatch: Address fields differ between MV1, BOS, DL, and Dealer DMV. County mismatch affects TAVT calculation.
Lien Holder Mismatch: Lien holder on MV1 doesn’t match BOS or Title/MSO.
Tax Amount Mismatch: Dealer DMV TAVT value doesn’t match Bill of Sale or MV1.
Document & Data Issues

Incomplete Reassignment (MV-7D): Reassignment form missing signatures, VIN, mileage, or transfer date.
Invalid or Expired Driver’s License: DL is expired, or information is illegible.
Financial / Regulatory Exceptions
TAVT Difference > $500: Tax calculation is off by more than $500; requires manual review.
Title Without Lien Release: Title shows a lien, but no proof of lien satisfaction is attached.
Incorrect Transaction Type: BOS or MV1 says “Cash,” but deal is actually Lease or Finance.
Timing & Status Exceptions
Deal Not Fully Finalized: Deal missing one or more eligibility conditions (e.g., not funded, not in accounting).
Title Not Yet Received: Deal assigned for processing before title or MSO is physically or digitally available.
Reassignment Needed but Not Included: Used vehicle with title fully reassigned, but no MV-7D included.
6.2 Reporting
6.3 Exception Emails

Appendix
The appendix contains reference materials used throughout the PDD.
7.1 Recordings
Here is the link for all the saved recordings:
Recordings
7.2 Sample Files
Here is the link where Sample files have been uploaded:
PDF Examples

